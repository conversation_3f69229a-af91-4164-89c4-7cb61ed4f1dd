# Circle CI Configuration لمشروع منتجاتي
version: 2.1

# تعريف المهام
jobs:
  # مهمة بناء Backend
  build-backend:
    docker:
      - image: node:18
    working_directory: ~/montajati
    steps:
      - checkout
      - run:
          name: Install Backend Dependencies
          command: |
            cd backend
            npm install
      - run:
          name: Run Backend Tests
          command: |
            cd backend
            npm run test:db
            npm run test:firebase
      - run:
          name: Build Backend
          command: |
            cd backend
            npm run production

  # مهمة بناء Frontend
  build-frontend:
    docker:
      - image: cirrusci/flutter:3.24.0
    working_directory: ~/montajati
    steps:
      - checkout
      - run:
          name: Flutter Doctor
          command: flutter doctor
      - run:
          name: Get Flutter Dependencies
          command: |
            cd frontend
            flutter pub get
      - run:
          name: Run Flutter Tests
          command: |
            cd frontend
            flutter test
      - run:
          name: Build APK
          command: |
            cd frontend
            flutter build apk --release

  # مهمة النشر
  deploy:
    docker:
      - image: node:18
    working_directory: ~/montajati
    steps:
      - checkout
      - run:
          name: Deploy to Render
          command: |
            echo "Deploying to production..."
            # هنا يمكن إضافة أوامر النشر

# تعريف سير العمل
workflows:
  version: 2
  build-and-deploy:
    jobs:
      - build-backend
      - build-frontend
      - deploy:
          requires:
            - build-backend
            - build-frontend
          filters:
            branches:
              only: main

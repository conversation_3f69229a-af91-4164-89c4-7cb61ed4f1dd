{"version": "0.2.0", "configurations": [{"name": "Flutter (Development with Production Server)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--debug"], "console": "debugConsole", "env": {"FLUTTER_MODE": "debug"}}, {"name": "Flutter Web (Chrome - Production Server)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["-d", "chrome", "--web-port", "3002", "--debug"], "console": "debugConsole", "env": {"FLUTTER_MODE": "debug"}}, {"name": "Flutter Android (Production Server)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--debug"], "console": "debugConsole", "deviceId": "android", "env": {"FLUTTER_MODE": "debug"}}, {"name": "Flutter Release (Production Ready)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--release"], "console": "debugConsole"}, {"name": "Flutter Debug (Android Emulator - Detailed Logs)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "emulator-5554", "args": ["--debug", "--verbose"], "console": "debugConsole", "logging": {"exceptions": true, "programOutput": true, "engineLogging": true}, "env": {"FLUTTER_MODE": "debug"}}]}
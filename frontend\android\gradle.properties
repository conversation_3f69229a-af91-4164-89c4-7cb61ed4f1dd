# إعدادات محسنة لحل مشاكل Kotlin daemon
org.gradle.jvmargs=-Xmx6G -XX:MaxMetaspaceSize=2G -XX:+UseG1GC -Dfile.encoding=UTF-8 -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# إعدادات Gradle محسنة
org.gradle.daemon=false
org.gradle.parallel=false
org.gradle.caching=false
org.gradle.configureondemand=false

# إعدادات Kotlin محسنة
kotlin.incremental=false
kotlin.daemon.enabled=false
kotlin.compiler.execution.strategy=in-process

# إعدادات Android محسنة
android.enableR8.fullMode=false
android.enableBuildCache=false

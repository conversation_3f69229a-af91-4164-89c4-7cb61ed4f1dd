name: monta<PERSON><PERSON>_app
description: "تطبيق منتجاتي - منصة الدروب شوبينغ الأولى في العراق"
homepage: https://montajati.com
repository: https://github.com/montajati/montajati-app
publish_to: 'none'

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 3.3.0+10

environment:
  sdk: ^3.8.1
  flutter: ">=3.32.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # أيقونات التطبيق
  cupertino_icons: ^1.0.8

  # التنقل والتوجيه - router.dart
  go_router: ^16.0.0

  # إدارة الحالة
  provider: ^6.1.2

  # طلبات HTTP للاتصال بالـ Backend
  http: ^1.4.0
  dio: ^5.7.0

  # تخزين محلي
  shared_preferences: ^2.5.3

  # الإشعارات والأذونات
  permission_handler: ^12.0.1

  # JSON والتشفير
  json_annotation: ^4.9.0
  crypto: ^3.0.6

  # واجهة المستخدم والرسوم المتحركة
  flutter_svg: ^2.2.0
  lottie: ^3.1.2
  animations: ^2.0.11
  flutter_staggered_animations: ^1.1.1

  # الخطوط والأيقونات
  google_fonts: ^6.2.1
  font_awesome_flutter: ^10.7.0

  # التعامل مع الصور
  image_picker: ^1.1.2
  cached_network_image: ^3.4.0

  # دمج JavaScript
  flutter_js: ^0.8.5

  # أدوات إضافية
  intl: ^0.20.2
  flutter_localizations:
    sdk: flutter
  url_launcher: ^6.3.2

  # تحميل الملفات
  universal_html: ^2.2.4
  path_provider: ^2.1.4

  # Supabase
  supabase_flutter: ^2.8.2

  # الرسوم البيانية
  fl_chart: ^0.69.2

  # توليد UUID
  uuid: ^4.5.1

  # Firebase للإشعارات الفورية - إصدارات متوافقة ومستقرة
  firebase_core: ^2.32.0
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^17.2.3
  device_info_plus: ^10.1.2







dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  build_runner: ^2.4.7

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # إضافة الأصول (الصور والخطوط)
  assets:
    - assets/images/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

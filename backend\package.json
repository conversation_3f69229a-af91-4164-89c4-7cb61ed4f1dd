{"name": "<PERSON><PERSON><PERSON><PERSON>-backend", "version": "1.0.0", "description": "Backend API لتطبيق منتجاتي - نظام إدارة الدروب شيبنگ", "main": "server.js", "scripts": {"start": "node official_montajati_server.js", "server": "node official_montajati_server.js", "start-simple": "node simple_test_server.js", "check-env": "node check_env_vars.js", "test-services": "node test_services.js", "legacy": "node start_system_complete.js", "start-debug": "node render-start.js", "dev": "nodemon official_montajati_server.js", "production": "NODE_ENV=production node start_production_system.js", "sync": "node production/main.js", "admin": "node production/admin_interface.js", "local": "node production_server.js", "proxy": "node flexible_delivery_proxy.js", "proxy:dev": "nodemon flexible_delivery_proxy.js", "monitor": "node order_status_monitor.js", "monitor:dev": "nodemon order_status_monitor.js", "flexible": "node start_flexible_system.js", "migrate": "node scripts/run_migrations.js", "migrate:create": "node scripts/run_migrations.js create", "test:firebase": "node scripts/test_firebase_connection.js", "test:db": "node scripts/test_database_connection.js", "test:notifications": "node scripts/test_complete_notification_system.js", "test:notification": "node scripts/send_test_notification.js", "setup": "npm run migrate && npm run test:firebase", "test-inventory": "node test_inventory_monitor.js", "health": "curl http://localhost:3003/health", "status": "curl http://localhost:3003/api/system/status", "metrics": "curl http://localhost:3003/api/monitor/metrics", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dropshipping", "ecommerce", "api", "nodejs", "express"], "author": "منتجاتي", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.50.4", "axios": "^1.6.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "cloudinary": "^2.6.1", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "form-data": "^4.0.3", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.2", "multer": "^2.0.1", "node-cron": "^3.0.3", "pg": "^8.16.0", "streamifier": "^0.1.1", "firebase-admin": "^12.7.0"}, "devDependencies": {"nodemon": "^3.1.10"}}
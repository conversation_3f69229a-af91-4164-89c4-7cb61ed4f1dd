{"augment.database.provider": "supabase", "augment.database.url": "https://fqdhskaolzfavapmqodl.supabase.co", "augment.database.key": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.tRHMAogrSzjRwSIJ9-m0YMoPhlHeR6U8kfob0wyvf_I", "augment.database.project": "fqdhskaolzfavapmqodl", "augment.connection.timeout": 30000, "augment.connection.retries": 3, "augment.connection.autoConnect": false, "augment.features.realtime": false, "augment.features.auth": true, "augment.features.storage": true, "augment.project.name": "منتجاتي - <PERSON><PERSON><PERSON>", "augment.project.type": "flutter_nodejs", "augment.project.language": "ar"}